file_specific_replacements:
  description: 针对特定文件的替换规则
  kafka.yaml:
  - description: 替换Kafka服务器地址
    name: Kafka服务器地址替换
    pattern: bootstrap-servers:\s*10\.1\.2\.45:9092,10\.1\.2\.46:9092,10\.1\.2\.47:9092
    replacement: 'bootstrap-servers: {kafka_servers}'
  mongodb-script.yaml:
  - description: 替换MongoDB script库连接信息
    name: MongoDB连接字符串替换
    pattern: **************************\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script
    replacement: mongodb://script:{mongodb_script_password}@{mongodb_hosts}/script
  redis.yaml:
  - description: 替换Redis密码
    name: Redis密码替换
    pattern: password:\s*"redis@cmdb3"
    replacement: 'password: "{redis_password}"'
  - description: 替换Redis哨兵节点
    name: Redis哨兵节点替换
    pattern: nodes:\s*10\.1\.2\.45:26379,10\.1\.2\.46:26379,10\.1\.2\.47:26379
    replacement: 'nodes: {redis_sentinel_nodes}'
global_replacements:
  description: 全局替换规则，适用于所有配置文件
  rules:
  - description: 将10.1.2.x替换为192.168.1.x
    name: IP地址替换
    pattern: 10\.1\.2\.\d+
    replacement: 192.168.1.{host_suffix}
  - description: 替换平台地址
    name: 平台地址替换
    pattern: http://10\.1\.2\.39:7100/
    replacement: http://{platform_host}:7100/
  - description: 替换网关地址
    name: 网关地址替换
    pattern: http://10\.1\.2\.39:7004/
    replacement: http://{gateway_host}:7004/
variables:
  description: 替换变量定义
  gateway_host: *************
  host_suffix_mapping:
    '39': '100'
    '45': '101'
    '46': '102'
    '47': '103'
  kafka_servers: *************:9092,*************:9092,*************:9092
  mongodb_hosts: *************:27017,*************:27017,*************:27017
  mongodb_script_password: newscriptpass
  platform_host: *************
  redis_password: newredispass123
  redis_sentinel_nodes: *************:26379,*************:26379,*************:26379
