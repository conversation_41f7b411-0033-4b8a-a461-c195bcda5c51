#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nacos配置替换工具使用示例
演示如何使用NacosConfigReplacer类进行配置替换
"""

from nacos_config_replacer import NacosConfigReplacer
import os

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 1. 创建替换器实例
    config_dir = "./DEFAULT_GROUP"
    backup_dir = "./backup"
    replacer = NacosConfigReplacer(config_dir, backup_dir)
    
    # 2. 创建默认配置模板
    print("创建默认配置模板...")
    template_file = replacer.create_default_replacement_config("my_replacement_config.yaml")
    print(f"配置模板已创建: {template_file}")
    
    # 3. 加载替换配置
    print("加载替换配置...")
    replacer.load_replacement_config(template_file)
    
    # 4. 处理所有配置文件
    print("开始处理配置文件...")
    results = replacer.process_all_configs()
    
    # 5. 显示结果
    print(f"处理结果: {results}")
    
    # 6. 生成报告
    report = replacer.generate_report()
    print("处理报告:")
    print(report)

def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    # 创建自定义替换配置
    custom_config = {
        "global_replacements": {
            "rules": [
                {
                    "name": "测试IP替换",
                    "pattern": r"10\.1\.2\.39",
                    "replacement": "*************",
                    "description": "将测试环境IP替换为生产环境IP"
                }
            ]
        },
        "file_specific_replacements": {
            "redis.yaml": [
                {
                    "name": "Redis密码替换",
                    "pattern": r'password:\s*"redis@cmdb3"',
                    "replacement": 'password: "production_redis_pass"',
                    "description": "替换Redis密码为生产环境密码"
                }
            ]
        },
        "variables": {
            "new_host": "*************",
            "redis_password": "production_redis_pass"
        }
    }
    
    # 保存自定义配置
    import yaml
    with open("custom_config.yaml", "w", encoding="utf-8") as f:
        yaml.dump(custom_config, f, default_flow_style=False, allow_unicode=True)
    
    # 使用自定义配置
    replacer = NacosConfigReplacer("./DEFAULT_GROUP")
    replacer.load_replacement_config("custom_config.yaml")
    
    # 只处理特定文件
    specific_files = ["redis.yaml", "common.yaml"]
    results = replacer.process_all_configs(specific_files)
    print(f"自定义配置处理结果: {results}")

def example_restore_backup():
    """备份恢复示例"""
    print("\n=== 备份恢复示例 ===")
    
    replacer = NacosConfigReplacer("./DEFAULT_GROUP")
    
    # 恢复特定文件
    print("恢复redis.yaml文件...")
    replacer.restore_from_backup("redis.yaml")
    
    # 恢复所有文件
    print("恢复所有备份文件...")
    replacer.restore_from_backup()

def example_step_by_step():
    """分步骤处理示例"""
    print("\n=== 分步骤处理示例 ===")
    
    replacer = NacosConfigReplacer("./DEFAULT_GROUP")
    
    # 步骤1: 创建并编辑配置模板
    print("步骤1: 创建配置模板")
    template_file = replacer.create_default_replacement_config("step_by_step_config.yaml")
    
    print(f"请编辑配置文件 {template_file}，然后按回车继续...")
    input()
    
    # 步骤2: 加载配置
    print("步骤2: 加载替换配置")
    try:
        replacer.load_replacement_config(template_file)
        print("配置加载成功")
    except Exception as e:
        print(f"配置加载失败: {e}")
        return
    
    # 步骤3: 确认要处理的文件
    config_files = list(replacer.config_dir.glob("*.yaml"))
    print(f"步骤3: 发现 {len(config_files)} 个配置文件:")
    for i, file_path in enumerate(config_files, 1):
        print(f"  {i}. {file_path.name}")
    
    confirm = input("是否继续处理这些文件? (y/n): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    # 步骤4: 执行替换
    print("步骤4: 执行配置替换")
    results = replacer.process_all_configs()
    
    # 步骤5: 显示结果和报告
    print("步骤5: 处理完成")
    print(f"修改了 {results['modified_files']} 个文件")
    
    if results['modified_files'] > 0:
        print("\n生成详细报告:")
        report = replacer.generate_report()
        print(report)
        
        # 保存报告
        with open("processing_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print("报告已保存到 processing_report.txt")

def main():
    """主函数"""
    print("Nacos配置替换工具使用示例")
    print("=" * 50)
    
    # 检查配置目录是否存在
    if not os.path.exists("./DEFAULT_GROUP"):
        print("错误: 配置目录 './DEFAULT_GROUP' 不存在")
        print("请确保在正确的目录下运行此示例")
        return
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_custom_config()
        example_restore_backup()
        
        # 交互式示例
        run_interactive = input("\n是否运行交互式分步骤示例? (y/n): ")
        if run_interactive.lower() == 'y':
            example_step_by_step()
        
        print("\n所有示例运行完成!")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
