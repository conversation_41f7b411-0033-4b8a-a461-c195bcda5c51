# Nacos配置自动替换工具

## 功能概述

这个工具用于在新环境部署时自动替换Nacos配置文件中的变更项，包括但不限于：

- **数据库连接信息**: MongoDB、MySQL、Redis、Kafka等
- **网络配置**: IP地址、端口号
- **认证信息**: 用户名、密码
- **路径配置**: 绝对路径、URL地址
- **平台配置**: 平台地址、网关地址等

## 特点

✅ **保持原有格式**: 不会改变配置文件的注释、换行、缩进等格式  
✅ **正则表达式支持**: 支持复杂的模式匹配和替换  
✅ **批量处理**: 一次性处理多个配置文件  
✅ **安全备份**: 自动备份原始文件，支持一键恢复  
✅ **灵活配置**: 支持全局规则和文件特定规则  
✅ **详细日志**: 完整的操作日志和处理报告  

## 安装依赖

```bash
pip install pyyaml
```

## 使用方法

### 1. 创建配置模板

首先创建默认的替换配置模板：

```bash
python nacos_config_replacer.py --config-dir ./DEFAULT_GROUP --create-template
```

这会生成 `replacement_config.yaml` 配置模板文件。

### 2. 修改配置模板

编辑 `replacement_config.yaml`，根据你的实际环境修改变量值：

```yaml
variables:
  platform_host: "*************"        # 新的平台主机地址
  gateway_host: "*************"         # 新的网关主机地址
  redis_password: "newredispass123"      # 新的Redis密码
  mongodb_script_password: "newscriptpass"  # 新的MongoDB密码
  # ... 其他变量
```

### 3. 执行替换

```bash
# 执行配置替换
python nacos_config_replacer.py --config-dir ./DEFAULT_GROUP --replacement-config replacement_config.yaml

# 试运行模式（不实际修改文件）
python nacos_config_replacer.py --config-dir ./DEFAULT_GROUP --replacement-config replacement_config.yaml --dry-run
```

### 4. 恢复备份（如需要）

```bash
# 恢复所有文件
python nacos_config_replacer.py --config-dir ./DEFAULT_GROUP --restore

# 恢复特定文件
python nacos_config_replacer.py --config-dir ./DEFAULT_GROUP --restore-file redis.yaml
```

## 配置文件说明

### 替换配置结构

```yaml
global_replacements:          # 全局替换规则
  rules:
    - name: "规则名称"
      pattern: "正则表达式模式"
      replacement: "替换内容"
      description: "规则描述"

file_specific_replacements:   # 文件特定替换规则
  "文件名.yaml":
    - name: "规则名称"
      pattern: "正则表达式模式"
      replacement: "替换内容"

variables:                    # 变量定义
  variable_name: "variable_value"
```

### 支持的变量替换

在 `replacement` 字段中可以使用 `{variable_name}` 格式引用变量：

```yaml
replacement: "mongodb://user:{mongodb_password}@{mongodb_hosts}/database"
```

### 特殊功能：IP地址映射

支持智能的IP地址段替换：

```yaml
pattern: r"10\.1\.2\.\d+"
replacement: "192.168.1.{host_suffix}"
variables:
  host_suffix_mapping:
    "45": "101"    # ********* -> *************
    "46": "102"    # ********* -> *************
    "47": "103"    # ********* -> *************
```

## 命令行参数

```
--config-dir, -d      配置文件目录（必需）
--replacement-config, -r  替换配置文件路径
--backup-dir, -b      备份目录路径（可选）
--create-template, -t 创建默认替换配置模板
--restore            从备份恢复所有文件
--restore-file       从备份恢复指定文件
--dry-run            试运行模式，不实际修改文件
```

## 示例场景

### 场景1：更换IP地址段

从测试环境 `10.1.2.x` 迁移到生产环境 `192.168.1.x`：

```yaml
global_replacements:
  rules:
    - name: "IP地址段替换"
      pattern: r"10\.1\.2\.(\d+)"
      replacement: "192.168.1.{host_suffix}"
variables:
  host_suffix_mapping:
    "39": "100"
    "45": "101"
    "46": "102"
    "47": "103"
```

### 场景2：更换数据库密码

批量更换所有数据库连接的密码：

```yaml
file_specific_replacements:
  "redis.yaml":
    - pattern: r'password:\s*"[^"]*"'
      replacement: 'password: "{redis_password}"'
  "mongodb-script.yaml":
    - pattern: r"mongodb://(\w+):([^@]+)@"
      replacement: "mongodb://\\1:{mongodb_password}@"
```

## 注意事项

1. **备份重要性**: 工具会自动备份，但建议在重要环境中先手动备份
2. **测试验证**: 替换后请验证配置文件的正确性
3. **正则表达式**: 编写正则表达式时要小心，避免误匹配
4. **编码格式**: 确保配置文件使用UTF-8编码

## 日志和报告

- 操作日志保存在 `nacos_config_replacer.log`
- 处理报告保存在 `nacos_replacement_report.txt`
- 备份文件保存在 `backup/` 目录（可自定义）

## 故障排除

### 常见问题

1. **文件编码错误**: 确保配置文件使用UTF-8编码
2. **正则表达式错误**: 检查正则表达式语法
3. **权限问题**: 确保有读写配置文件的权限
4. **路径问题**: 检查配置目录路径是否正确

### 调试技巧

1. 使用 `--dry-run` 参数先测试
2. 查看日志文件了解详细错误信息
3. 从简单的替换规则开始测试
4. 使用 `--restore` 快速恢复原始文件
