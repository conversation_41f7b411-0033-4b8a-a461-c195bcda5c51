metadata:
- appName: ''
  dataId: ops-data-model.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: ops-script-repertory.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: ops-synapplication-activiti.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: quickops-flowdesigen.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: scheduled-proxy.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: kafka.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: mongodb-script.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: redis.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: common-workbench.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: ops-synapplication.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: ops-config-center.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: work-order.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: message-service.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: flow-sso.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: event-param.yaml
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: log-handler-server.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: elasticsearch.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: usercenter.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: gateway.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: ops-license.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: apply-api-manage.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: common.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: api-component.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: low-code-web.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: component-service.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: script-engine-dev.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: apply-api-manage-prod.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: platform-docking-service.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: sso.yaml
  desc: sso.yaml
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: salm2sso.yaml
  desc: salm2sso.yaml
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: fileUpDwn.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: prometheus.yaml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: saml2sso.yml
  desc: ''
  group: DEFAULT_GROUP
  type: yaml
- appName: ''
  dataId: batch-server.yaml
  desc: |
    batch-server.yaml
  group: DEFAULT_GROUP
  type: yaml
