# Nacos配置自动替换工具 - 使用说明

## 🎯 工具概述

这个工具专门用于在新环境部署时自动替换Nacos配置文件中的环境相关配置项，确保配置的准确性和一致性。

### ✨ 主要功能

- **🔄 自动替换**: 批量替换IP地址、数据库连接、密码等配置项
- **📁 安全备份**: 自动备份原始文件，支持一键恢复
- **🎨 保持格式**: 完全保持原有的注释、换行、缩进等格式
- **🎯 精确匹配**: 使用正则表达式确保精确替换，避免误操作
- **📊 详细报告**: 提供完整的操作日志和处理报告

### 🔧 支持的配置项

| 配置类型 | 示例 | 说明 |
|---------|------|------|
| **IP地址** | `*********` → `*************` | 自动映射IP地址段 |
| **MongoDB连接** | `************************:port/db` | 替换主机、密码 |
| **Redis配置** | 密码、哨兵节点 | 替换连接信息 |
| **Kafka配置** | `bootstrap-servers` | 替换服务器地址 |
| **平台地址** | `http://*********:7100/` | 替换平台URL |
| **数据库密码** | 各种数据库的用户名密码 | 批量更新认证信息 |

## 🚀 快速开始

### 方法一：交互式快速开始（推荐）

```bash
# 运行交互式配置工具
python quick_start.py
```

这个脚本会引导您：
1. 📝 输入新环境的配置信息
2. ⚙️ 自动生成替换配置文件
3. 🔄 执行配置替换
4. 📊 显示处理结果

### 方法二：命令行方式

```bash
# 1. 创建配置模板
python nacos_config_replacer.py --config-dir ./Nacos配置替换/DEFAULT_GROUP --create-template

# 2. 编辑 replacement_config.yaml 文件，修改变量值

# 3. 执行替换
python nacos_config_replacer.py --config-dir ./Nacos配置替换/DEFAULT_GROUP --replacement-config replacement_config.yaml

# 4. 如需恢复
python nacos_config_replacer.py --config-dir ./Nacos配置替换/DEFAULT_GROUP --restore
```

## 📋 配置示例

### 典型的环境迁移场景

**从测试环境迁移到生产环境：**

| 配置项 | 测试环境 | 生产环境 |
|--------|----------|----------|
| 平台主机 | `*********` | `*************` |
| 数据库集群 | `*********/46/47` | `*************/102/103` |
| Redis密码 | `redis@cmdb3` | `prod_redis_2024` |
| MongoDB密码 | `script` | `prod_script_2024` |

### 配置文件示例

```yaml
variables:
  # 主机配置
  platform_host: "*************"
  gateway_host: "*************"
  
  # 数据库集群
  mongodb_hosts: "*************:27017,*************:27017,*************:27017"
  kafka_servers: "*************:9092,*************:9092,*************:9092"
  redis_sentinel_nodes: "*************:26379,*************:26379,*************:26379"
  
  # 密码配置
  redis_password: "prod_redis_2024"
  mongodb_script_password: "prod_script_2024"
  mongodb_flow_password: "prod_flow_2024^&*"
  # ... 其他密码
```

## 📊 处理结果示例

```
处理结果:
总文件数: 34
处理成功: 34
修改文件: 15
失败文件: 0

已处理的文件:
  - redis.yaml (替换了5处配置)
  - mongodb-script.yaml (替换了3处配置)
  - ops-synapplication.yaml (替换了18处配置)
  - common.yaml (替换了2处配置)
  - kafka.yaml (替换了1处配置)
  ...
```

## 🔍 验证替换结果

### 1. 检查关键配置文件

```bash
# 检查Redis配置
cat Nacos配置替换/DEFAULT_GROUP/redis.yaml | grep -E "(password|nodes)"

# 检查MongoDB配置
cat Nacos配置替换/DEFAULT_GROUP/mongodb-script.yaml | grep "mongodb://"

# 检查平台地址
cat Nacos配置替换/DEFAULT_GROUP/common.yaml | grep "platform_address"
```

### 2. 对比备份文件

```bash
# 对比原始文件和修改后的文件
diff Nacos配置替换/backup/redis.yaml Nacos配置替换/DEFAULT_GROUP/redis.yaml
```

## 🛡️ 安全特性

### 自动备份
- ✅ 处理前自动备份所有文件到 `backup/` 目录
- ✅ 支持一键恢复所有文件或指定文件
- ✅ 备份文件保持原始时间戳

### 格式保护
- ✅ 完全保持YAML格式和缩进
- ✅ 保留所有注释内容
- ✅ 保持原有的换行和空行

### 精确替换
- ✅ 使用正则表达式精确匹配
- ✅ 避免误替换相似内容
- ✅ 支持复杂的替换规则

## 🔧 高级用法

### 自定义替换规则

```yaml
file_specific_replacements:
  "your-config.yaml":
    - name: "自定义规则"
      pattern: r"old_pattern"
      replacement: "new_value"
      description: "规则说明"
```

### 批量处理特定文件

```bash
# 只处理Redis和MongoDB配置
python nacos_config_replacer.py --config-dir ./Nacos配置替换/DEFAULT_GROUP --replacement-config config.yaml --file-patterns "redis.yaml,mongodb-*.yaml"
```

### 试运行模式

```bash
# 查看会进行哪些替换，但不实际修改文件
python nacos_config_replacer.py --config-dir ./Nacos配置替换/DEFAULT_GROUP --replacement-config config.yaml --dry-run
```

## 🚨 注意事项

### ⚠️ 使用前必读

1. **备份重要性**: 虽然工具会自动备份，但建议在重要环境中先手动备份整个配置目录
2. **测试验证**: 替换后请务必验证配置文件的正确性和完整性
3. **权限检查**: 确保有读写配置文件目录的权限
4. **编码格式**: 确保所有配置文件使用UTF-8编码

### 🔍 故障排除

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 文件编码错误 | 配置文件不是UTF-8编码 | 转换文件编码为UTF-8 |
| 正则表达式错误 | 替换规则语法错误 | 检查正则表达式语法 |
| 权限被拒绝 | 没有文件读写权限 | 检查文件和目录权限 |
| 路径不存在 | 配置目录路径错误 | 确认配置目录路径 |

### 📞 获取帮助

```bash
# 查看帮助信息
python nacos_config_replacer.py --help

# 查看详细日志
cat nacos_config_replacer.log

# 查看处理报告
cat nacos_replacement_report.txt
```

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `nacos_config_replacer.py` | 主要的替换工具 |
| `quick_start.py` | 交互式快速开始脚本 |
| `replacement_config_template.yaml` | 详细的配置模板 |
| `example_usage.py` | 使用示例代码 |
| `README_nacos_replacer.md` | 详细技术文档 |
| `使用说明.md` | 本文档 |

---

🎉 **祝您使用愉快！如有问题，请查看日志文件或联系技术支持。**
