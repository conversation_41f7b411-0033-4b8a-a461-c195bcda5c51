#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nacos配置替换工具 - 快速开始脚本
提供交互式界面，帮助用户快速完成配置替换
"""

import os
import sys
import yaml
from pathlib import Path
from nacos_config_replacer import NacosConfigReplacer

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 Nacos配置自动替换工具 - 快速开始")
    print("=" * 60)
    print("此工具将帮助您快速替换Nacos配置中的环境相关配置项")
    print("包括：IP地址、数据库连接、密码、路径等")
    print("=" * 60)

def get_user_input(prompt, default=None, required=True):
    """获取用户输入"""
    if default:
        prompt += f" (默认: {default})"
    prompt += ": "
    
    while True:
        value = input(prompt).strip()
        if value:
            return value
        elif default:
            return default
        elif not required:
            return ""
        else:
            print("此项为必填项，请输入有效值")

def collect_environment_config():
    """收集环境配置信息"""
    print("\n📋 请输入新环境的配置信息:")
    print("-" * 40)
    
    config = {}
    
    # 基础网络配置
    print("\n🌐 网络配置:")
    config['platform_host'] = get_user_input("平台主机地址", "*************")
    config['gateway_host'] = get_user_input("网关主机地址", config['platform_host'])
    
    # 数据库主机配置
    print("\n🗄️ 数据库主机配置:")
    db_host1 = get_user_input("数据库主机1", "*************")
    db_host2 = get_user_input("数据库主机2", "*************") 
    db_host3 = get_user_input("数据库主机3", "*************")
    
    config['mongodb_hosts'] = f"{db_host1}:27017,{db_host2}:27017,{db_host3}:27017"
    config['kafka_servers'] = f"{db_host1}:9092,{db_host2}:9092,{db_host3}:9092"
    config['redis_sentinel_nodes'] = f"{db_host1}:26379,{db_host2}:26379,{db_host3}:26379"
    
    # IP映射配置
    config['host_suffix_mapping'] = {
        "39": config['platform_host'].split('.')[-1],
        "45": db_host1.split('.')[-1],
        "46": db_host2.split('.')[-1], 
        "47": db_host3.split('.')[-1]
    }
    
    # 数据库密码配置
    print("\n🔐 数据库密码配置:")
    config['redis_password'] = get_user_input("Redis密码", "newredispass123")
    config['redis_sentinel_password'] = config['redis_password']
    
    config['mongodb_script_password'] = get_user_input("MongoDB script库密码", "newscriptpass")
    config['mongodb_flow_password'] = get_user_input("MongoDB flow库密码", "newflowpass^&*")
    config['mongodb_flow_prod_password'] = get_user_input("MongoDB flow-prod库密码", "newflowprodpass")
    config['mongodb_platform_password'] = get_user_input("MongoDB platform库密码", "newplatformpass^&*")
    config['mongodb_platform_prod_password'] = get_user_input("MongoDB platform-prod库密码", "newplatformprodpass^&*")
    config['mongodb_commondb_password'] = get_user_input("MongoDB commondb库密码", "newcommondbpass^&*")
    
    # 平台配置
    print("\n⚙️ 平台配置:")
    config['platform_prefix'] = get_user_input("平台前缀", "prod")
    config['es_prefix'] = get_user_input("Elasticsearch前缀", config['platform_prefix'])
    config['admin_password'] = get_user_input("管理员密码", "newadminpass^&*")
    
    return config

def create_replacement_config(config, output_file="custom_replacement_config.yaml"):
    """创建替换配置文件"""
    replacement_config = {
        "global_replacements": {
            "description": "全局替换规则，适用于所有配置文件",
            "rules": [
                {
                    "name": "IP地址段替换",
                    "pattern": r"10\.1\.2\.(\d+)",
                    "replacement": "192.168.1.{host_suffix}",
                    "description": "将10.1.2.x替换为192.168.1.x，支持智能映射"
                },
                {
                    "name": "平台地址替换",
                    "pattern": r"http://10\.1\.2\.39:7100/",
                    "replacement": f"http://{config['platform_host']}:7100/",
                    "description": "替换平台地址"
                },
                {
                    "name": "网关地址替换",
                    "pattern": r"http://10\.1\.2\.39:7004/",
                    "replacement": f"http://{config['gateway_host']}:7004/",
                    "description": "替换网关地址"
                }
            ]
        },
        "file_specific_replacements": {
            "description": "针对特定文件的替换规则",
            "redis.yaml": [
                {
                    "name": "Redis密码替换",
                    "pattern": r'password:\s*"redis@cmdb3"',
                    "replacement": f'password: "{config["redis_password"]}"',
                    "description": "替换Redis密码"
                },
                {
                    "name": "Redis哨兵节点替换",
                    "pattern": r"nodes:\s*10\.1\.2\.45:26379,10\.1\.2\.46:26379,10\.1\.2\.47:26379",
                    "replacement": f"nodes: {config['redis_sentinel_nodes']}",
                    "description": "替换Redis哨兵节点"
                }
            ],
            "mongodb-script.yaml": [
                {
                    "name": "MongoDB script库连接替换",
                    "pattern": r"**************************\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script",
                    "replacement": f"mongodb://script:{config['mongodb_script_password']}@{config['mongodb_hosts']}/script",
                    "description": "替换MongoDB script库连接信息"
                }
            ],
            "kafka.yaml": [
                {
                    "name": "Kafka服务器地址替换",
                    "pattern": r"bootstrap-servers:\s*10\.1\.2\.45:9092,10\.1\.2\.46:9092,10\.1\.2\.47:9092",
                    "replacement": f"bootstrap-servers: {config['kafka_servers']}",
                    "description": "替换Kafka服务器地址"
                }
            ],
            "common.yaml": [
                {
                    "name": "平台前缀替换",
                    "pattern": r"platform_prefix:\s*ae",
                    "replacement": f"platform_prefix: {config['platform_prefix']}",
                    "description": "替换平台前缀标识"
                },
                {
                    "name": "ES前缀替换",
                    "pattern": r"prefix:\s*ae",
                    "replacement": f"prefix: {config['es_prefix']}",
                    "description": "替换Elasticsearch前缀"
                }
            ],
            "ops-synapplication.yaml": [
                {
                    "name": "管理员密码替换",
                    "pattern": r"adminPassword:\s*root\^\&\*",
                    "replacement": f"adminPassword: {config['admin_password']}",
                    "description": "替换管理员密码"
                },
                {
                    "name": "MongoDB flow库连接替换",
                    "pattern": r"mongodb://flow:flow\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/flow",
                    "replacement": f"mongodb://flow:{config['mongodb_flow_password']}@{config['mongodb_hosts']}/flow",
                    "description": "替换MongoDB flow库连接"
                },
                {
                    "name": "MongoDB script库连接替换",
                    "pattern": r"mongodb://script:script\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script",
                    "replacement": f"mongodb://script:{config['mongodb_script_password']}@{config['mongodb_hosts']}/script",
                    "description": "替换MongoDB script库连接"
                },
                {
                    "name": "MongoDB commondb库连接替换",
                    "pattern": r"mongodb://commondb:commondb\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/commondb",
                    "replacement": f"mongodb://commondb:{config['mongodb_commondb_password']}@{config['mongodb_hosts']}/commondb",
                    "description": "替换MongoDB commondb库连接"
                }
            ]
        },
        "variables": config
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        yaml.dump(replacement_config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    return output_file

def main():
    """主函数"""
    print_banner()
    
    # 检查配置目录
    config_dir = "./DEFAULT_GROUP"
    if not os.path.exists(config_dir):
        print(f"❌ 错误: 配置目录 '{config_dir}' 不存在")
        print("请确保在正确的目录下运行此脚本")
        return 1
    
    try:
        # 步骤1: 收集配置信息
        print("\n📝 步骤1: 收集环境配置信息")
        config = collect_environment_config()
        
        # 步骤2: 创建替换配置文件
        print("\n⚙️ 步骤2: 生成替换配置文件")
        config_file = create_replacement_config(config)
        print(f"✅ 替换配置文件已生成: {config_file}")
        
        # 步骤3: 确认配置
        print(f"\n📋 步骤3: 配置预览")
        print(f"平台地址: {config['platform_host']}")
        print(f"数据库集群: {config['mongodb_hosts']}")
        print(f"Redis集群: {config['redis_sentinel_nodes']}")
        print(f"Kafka集群: {config['kafka_servers']}")
        
        confirm = get_user_input("\n是否继续执行配置替换", "y", False)
        if confirm.lower() not in ['y', 'yes', '是']:
            print("❌ 操作已取消")
            return 0
        
        # 步骤4: 执行替换
        print("\n🔄 步骤4: 执行配置替换")
        replacer = NacosConfigReplacer(config_dir)
        replacer.load_replacement_config(config_file)
        
        # 先试运行
        print("正在执行试运行检查...")
        results = replacer.process_all_configs()
        
        print(f"\n📊 替换结果:")
        print(f"总文件数: {results['total_files']}")
        print(f"需要修改的文件: {results['modified_files']}")
        print(f"处理成功: {results['processed_files']}")
        print(f"处理失败: {results['failed_files']}")
        
        if results['modified_files'] > 0:
            print(f"\n✅ 配置替换完成!")
            print(f"📁 备份目录: {replacer.backup_dir}")
            print(f"📄 详细日志: nacos_config_replacer.log")
            
            # 生成报告
            report = replacer.generate_report()
            with open("replacement_report.txt", "w", encoding="utf-8") as f:
                f.write(report)
            print(f"📋 处理报告: replacement_report.txt")
            
            print(f"\n💡 提示:")
            print(f"- 如需恢复原始配置，请运行: python nacos_config_replacer.py --config-dir {config_dir} --restore")
            print(f"- 请验证替换后的配置文件是否正确")
        else:
            print("ℹ️ 没有文件需要修改")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
