file_specific_replacements:
  common.yaml:
  - description: 替换平台前缀标识
    name: 平台前缀替换
    pattern: platform_prefix:\s*ae
    replacement: 'platform_prefix: gq'
  - description: 替换Elasticsearch前缀
    name: ES前缀替换
    pattern: prefix:\s*ae
    replacement: 'prefix: gq'
  description: 针对特定文件的替换规则
  kafka.yaml:
  - description: 替换Kafka服务器地址
    name: Kafka服务器地址替换
    pattern: bootstrap-servers:\s*10\.1\.2\.45:9092,10\.1\.2\.46:9092,10\.1\.2\.47:9092
    replacement: 'bootstrap-servers: **********:9092,**********:9092,**********:9092'
  mongodb-script.yaml:
  - description: 替换MongoDB script库连接信息
    name: MongoDB script库连接替换
    pattern: **************************\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script
    replacement: *********************************************************************************
  ops-synapplication.yaml:
  - description: 替换管理员密码
    name: 管理员密码替换
    pattern: adminPassword:\s*root\^\&\*
    replacement: 'adminPassword: admin'
  - description: 替换MongoDB flow库连接
    name: MongoDB flow库连接替换
    pattern: mongodb://flow:flow\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/flow
    replacement: ***************************************************************************
  - description: 替换MongoDB script库连接
    name: MongoDB script库连接替换
    pattern: mongodb://script:script\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script
    replacement: *********************************************************************************
  - description: 替换MongoDB commondb库连接
    name: MongoDB commondb库连接替换
    pattern: mongodb://commondb:commondb\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/commondb
    replacement: ***************************************************************************************
  redis.yaml:
  - description: 替换Redis密码
    name: Redis密码替换
    pattern: password:\s*"redis@cmdb3"
    replacement: 'password: "master123"'
  - description: 替换Redis哨兵节点
    name: Redis哨兵节点替换
    pattern: nodes:\s*10\.1\.2\.45:26379,10\.1\.2\.46:26379,10\.1\.2\.47:26379
    replacement: 'nodes: **********:26379,**********:26379,**********:26379'
global_replacements:
  description: 全局替换规则，适用于所有配置文件
  rules:
  - description: 将10.1.2.x替换为192.168.1.x，支持智能映射
    name: IP地址段替换
    pattern: 10\.1\.2\.(\d+)
    replacement: 192.168.1.{host_suffix}
  - description: 替换平台地址
    name: 平台地址替换
    pattern: http://10\.1\.2\.39:7100/
    replacement: http://**********:7100/
  - description: 替换网关地址
    name: 网关地址替换
    pattern: http://10\.1\.2\.39:7004/
    replacement: http://**********:7004/
variables:
  admin_password: admin
  es_prefix: gq
  gateway_host: **********
  host_suffix_mapping:
    '39': '229'
    '45': '237'
    '46': '236'
    '47': '235'
  kafka_servers: **********:9092,**********:9092,**********:9092
  mongodb_commondb_password: commondb
  mongodb_flow_password: flow
  mongodb_flow_prod_password: flow-prod
  mongodb_hosts: **********:27017,**********:27017,**********:27017
  mongodb_platform_password: platform
  mongodb_platform_prod_password: platform-prod
  mongodb_script_password: script
  platform_host: **********
  platform_prefix: gq
  redis_password: master123
  redis_sentinel_nodes: **********:26379,**********:26379,**********:26379
  redis_sentinel_password: master123
