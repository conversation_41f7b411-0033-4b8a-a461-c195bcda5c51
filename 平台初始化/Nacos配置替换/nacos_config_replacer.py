#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nacos配置自动替换工具
用于在新环境部署时自动替换配置文件中的变更项，包括但不限于：
- MongoDB、MySQL、Redis、Kafka等数据库连接信息
- IP地址和端口
- 用户名和密码
- 绝对路径
- 平台地址等

特点：
- 保持原有配置文件的格式、注释和换行
- 支持正则表达式匹配和替换
- 支持批量处理多个配置文件
- 提供配置模板和替换规则
"""

import os
import re
import json
import yaml
import shutil
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nacos_config_replacer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class NacosConfigReplacer:
    """Nacos配置替换器"""
    
    def __init__(self, config_dir: str, backup_dir: str = None):
        """
        初始化配置替换器
        
        Args:
            config_dir: 配置文件目录
            backup_dir: 备份目录，如果不指定则自动创建
        """
        self.config_dir = Path(config_dir)
        self.backup_dir = Path(backup_dir) if backup_dir else self.config_dir.parent / "backup"
        self.replacement_rules = {}
        self.processed_files = []
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def load_replacement_config(self, config_file: str):
        """
        加载替换配置文件
        
        Args:
            config_file: 替换配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.json'):
                    self.replacement_rules = json.load(f)
                elif config_file.endswith(('.yml', '.yaml')):
                    self.replacement_rules = yaml.safe_load(f)
                else:
                    raise ValueError("配置文件格式不支持，请使用JSON或YAML格式")
            
            logger.info(f"成功加载替换配置: {config_file}")
            
        except Exception as e:
            logger.error(f"加载替换配置失败: {e}")
            raise
    
    def create_default_replacement_config(self, output_file: str = "replacement_config.yaml"):
        """
        创建默认的替换配置模板
        
        Args:
            output_file: 输出配置文件名
        """
        default_config = {
            "global_replacements": {
                "description": "全局替换规则，适用于所有配置文件",
                "rules": [
                    {
                        "name": "IP地址替换",
                        "pattern": r"10\.1\.2\.\d+",
                        "replacement": "192.168.1.{host_suffix}",
                        "description": "将10.1.2.x替换为192.168.1.x"
                    },
                    {
                        "name": "平台地址替换",
                        "pattern": r"http://10\.1\.2\.39:7100/",
                        "replacement": "http://{platform_host}:7100/",
                        "description": "替换平台地址"
                    },
                    {
                        "name": "网关地址替换", 
                        "pattern": r"http://10\.1\.2\.39:7004/",
                        "replacement": "http://{gateway_host}:7004/",
                        "description": "替换网关地址"
                    }
                ]
            },
            "file_specific_replacements": {
                "description": "针对特定文件的替换规则",
                "redis.yaml": [
                    {
                        "name": "Redis密码替换",
                        "pattern": r'password:\s*"redis@cmdb3"',
                        "replacement": 'password: "{redis_password}"',
                        "description": "替换Redis密码"
                    },
                    {
                        "name": "Redis哨兵节点替换",
                        "pattern": r"nodes:\s*10\.1\.2\.45:26379,10\.1\.2\.46:26379,10\.1\.2\.47:26379",
                        "replacement": "nodes: {redis_sentinel_nodes}",
                        "description": "替换Redis哨兵节点"
                    }
                ],
                "mongodb-script.yaml": [
                    {
                        "name": "MongoDB连接字符串替换",
                        "pattern": r"**************************\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script",
                        "replacement": "mongodb://script:{mongodb_script_password}@{mongodb_hosts}/script",
                        "description": "替换MongoDB script库连接信息"
                    }
                ],
                "kafka.yaml": [
                    {
                        "name": "Kafka服务器地址替换",
                        "pattern": r"bootstrap-servers:\s*10\.1\.2\.45:9092,10\.1\.2\.46:9092,10\.1\.2\.47:9092",
                        "replacement": "bootstrap-servers: {kafka_servers}",
                        "description": "替换Kafka服务器地址"
                    }
                ]
            },
            "variables": {
                "description": "替换变量定义",
                "platform_host": "*************",
                "gateway_host": "*************", 
                "redis_password": "newredispass123",
                "redis_sentinel_nodes": "*************:26379,*************:26379,*************:26379",
                "mongodb_hosts": "*************:27017,*************:27017,*************:27017",
                "mongodb_script_password": "newscriptpass",
                "kafka_servers": "*************:9092,*************:9092,*************:9092",
                "host_suffix_mapping": {
                    "45": "101",
                    "46": "102", 
                    "47": "103",
                    "39": "100"
                }
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        logger.info(f"已创建默认替换配置模板: {output_file}")
        return output_file

    def backup_file(self, file_path: Path) -> Path:
        """
        备份文件

        Args:
            file_path: 要备份的文件路径

        Returns:
            备份文件路径
        """
        backup_file_path = self.backup_dir / file_path.name
        shutil.copy2(file_path, backup_file_path)
        logger.info(f"已备份文件: {file_path} -> {backup_file_path}")
        return backup_file_path

    def apply_replacements(self, content: str, rules: List[Dict], variables: Dict) -> str:
        """
        应用替换规则到内容

        Args:
            content: 原始内容
            rules: 替换规则列表
            variables: 变量字典

        Returns:
            替换后的内容
        """
        modified_content = content

        for rule in rules:
            pattern = rule.get('pattern')
            replacement = rule.get('replacement')
            name = rule.get('name', '未命名规则')

            if not pattern or replacement is None:
                logger.warning(f"跳过无效规则: {name}")
                continue

            try:
                # 处理特殊的IP地址映射
                if '{host_suffix}' in replacement:
                    def replace_ip(match):
                        ip = match.group(0)
                        last_octet = ip.split('.')[-1]
                        mapping = variables.get('host_suffix_mapping', {})
                        new_suffix = mapping.get(last_octet, last_octet)
                        return replacement.format(host_suffix=new_suffix)

                    new_content = re.sub(pattern, replace_ip, modified_content)
                else:
                    # 格式化替换字符串
                    formatted_replacement = replacement.format(**variables)
                    new_content = re.sub(pattern, formatted_replacement, modified_content)

                if new_content != modified_content:
                    matches = len(re.findall(pattern, modified_content))
                    logger.info(f"应用规则 '{name}': 替换了 {matches} 处匹配")
                    modified_content = new_content

            except Exception as e:
                logger.error(f"应用规则 '{name}' 时出错: {e}")
                continue

        return modified_content

    def process_file(self, file_path: Path) -> bool:
        """
        处理单个配置文件

        Args:
            file_path: 配置文件路径

        Returns:
            是否成功处理
        """
        try:
            # 备份原文件
            self.backup_file(file_path)

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            modified_content = original_content
            variables = self.replacement_rules.get('variables', {})

            # 应用全局替换规则
            global_rules = self.replacement_rules.get('global_replacements', {}).get('rules', [])
            if global_rules:
                logger.info(f"对文件 {file_path.name} 应用全局替换规则")
                modified_content = self.apply_replacements(modified_content, global_rules, variables)

            # 应用文件特定替换规则
            file_specific_rules = self.replacement_rules.get('file_specific_replacements', {}).get(file_path.name, [])
            if file_specific_rules:
                logger.info(f"对文件 {file_path.name} 应用特定替换规则")
                modified_content = self.apply_replacements(modified_content, file_specific_rules, variables)

            # 如果内容有变化，写入文件
            if modified_content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                logger.info(f"已更新配置文件: {file_path}")
                self.processed_files.append(str(file_path))
                return True
            else:
                logger.info(f"文件 {file_path.name} 无需更改")
                return False

        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return False

    def process_all_configs(self, file_patterns: List[str] = None) -> Dict[str, Any]:
        """
        批量处理所有配置文件

        Args:
            file_patterns: 文件模式列表，如果不指定则处理所有.yaml和.yml文件

        Returns:
            处理结果统计
        """
        if file_patterns is None:
            file_patterns = ['*.yaml', '*.yml']

        all_files = []
        for pattern in file_patterns:
            all_files.extend(self.config_dir.glob(pattern))

        if not all_files:
            logger.warning(f"在目录 {self.config_dir} 中未找到匹配的配置文件")
            return {"success": False, "message": "未找到配置文件"}

        results = {
            "total_files": len(all_files),
            "processed_files": 0,
            "modified_files": 0,
            "failed_files": 0,
            "processed_file_list": [],
            "failed_file_list": []
        }

        logger.info(f"开始处理 {len(all_files)} 个配置文件")

        for file_path in all_files:
            logger.info(f"正在处理: {file_path.name}")

            try:
                success = self.process_file(file_path)
                results["processed_files"] += 1

                if success:
                    results["modified_files"] += 1
                    results["processed_file_list"].append(str(file_path))

            except Exception as e:
                logger.error(f"处理文件 {file_path} 失败: {e}")
                results["failed_files"] += 1
                results["failed_file_list"].append(str(file_path))

        logger.info(f"处理完成: 总计 {results['total_files']} 个文件, "
                   f"成功处理 {results['processed_files']} 个, "
                   f"修改 {results['modified_files']} 个, "
                   f"失败 {results['failed_files']} 个")

        return results

    def restore_from_backup(self, file_name: str = None):
        """
        从备份恢复文件

        Args:
            file_name: 要恢复的文件名，如果不指定则恢复所有备份文件
        """
        if file_name:
            backup_file = self.backup_dir / file_name
            target_file = self.config_dir / file_name

            if backup_file.exists():
                shutil.copy2(backup_file, target_file)
                logger.info(f"已从备份恢复文件: {file_name}")
            else:
                logger.error(f"备份文件不存在: {backup_file}")
        else:
            # 恢复所有备份文件
            backup_files = list(self.backup_dir.glob('*'))
            for backup_file in backup_files:
                target_file = self.config_dir / backup_file.name
                shutil.copy2(backup_file, target_file)
                logger.info(f"已从备份恢复文件: {backup_file.name}")

            logger.info(f"已恢复 {len(backup_files)} 个备份文件")

    def generate_report(self) -> str:
        """
        生成处理报告

        Returns:
            报告内容
        """
        report = []
        report.append("=" * 60)
        report.append("Nacos配置替换处理报告")
        report.append("=" * 60)
        report.append(f"配置目录: {self.config_dir}")
        report.append(f"备份目录: {self.backup_dir}")
        report.append(f"处理的文件数量: {len(self.processed_files)}")
        report.append("")

        if self.processed_files:
            report.append("已处理的文件:")
            for file_path in self.processed_files:
                report.append(f"  - {file_path}")
        else:
            report.append("没有文件被修改")

        report.append("")
        report.append("=" * 60)

        return "\n".join(report)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Nacos配置自动替换工具')
    parser.add_argument('--config-dir', '-d', required=True, help='配置文件目录')
    parser.add_argument('--replacement-config', '-r', help='替换配置文件路径')
    parser.add_argument('--backup-dir', '-b', help='备份目录路径')
    parser.add_argument('--create-template', '-t', action='store_true', help='创建默认替换配置模板')
    parser.add_argument('--restore', action='store_true', help='从备份恢复所有文件')
    parser.add_argument('--restore-file', help='从备份恢复指定文件')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际修改文件')

    args = parser.parse_args()

    # 创建替换器实例
    replacer = NacosConfigReplacer(args.config_dir, args.backup_dir)

    try:
        # 创建模板
        if args.create_template:
            template_file = replacer.create_default_replacement_config()
            print(f"已创建默认配置模板: {template_file}")
            print("请根据实际环境修改配置模板中的变量值，然后使用 -r 参数指定配置文件进行替换")
            return

        # 恢复文件
        if args.restore:
            replacer.restore_from_backup()
            print("已从备份恢复所有文件")
            return

        if args.restore_file:
            replacer.restore_from_backup(args.restore_file)
            print(f"已从备份恢复文件: {args.restore_file}")
            return

        # 执行替换
        if not args.replacement_config:
            print("错误: 请使用 -r 参数指定替换配置文件，或使用 -t 创建配置模板")
            return

        # 加载替换配置
        replacer.load_replacement_config(args.replacement_config)

        if args.dry_run:
            print("试运行模式: 将显示要进行的替换但不实际修改文件")
            # 在试运行模式下，可以添加预览功能

        # 执行批量替换
        results = replacer.process_all_configs()

        # 显示结果
        print("\n处理结果:")
        print(f"总文件数: {results['total_files']}")
        print(f"处理成功: {results['processed_files']}")
        print(f"修改文件: {results['modified_files']}")
        print(f"失败文件: {results['failed_files']}")

        if results['failed_file_list']:
            print("\n失败的文件:")
            for file_path in results['failed_file_list']:
                print(f"  - {file_path}")

        # 生成报告
        report = replacer.generate_report()
        print("\n" + report)

        # 保存报告到文件
        with open('nacos_replacement_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print("\n详细报告已保存到: nacos_replacement_report.txt")

    except Exception as e:
        logger.error(f"执行过程中出现错误: {e}")
        print(f"错误: {e}")
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
