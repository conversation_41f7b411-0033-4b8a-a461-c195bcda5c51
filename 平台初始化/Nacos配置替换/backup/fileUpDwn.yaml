server:
  port: 9595
spring:
  servlet:
    multipart:
      # 上传文件大小限制
      max-file-size: 5120MB
      max-request-size: 5120MB
  jackson:
    serialization:
      write-dates-as-timestamps: true
    default-property-inclusion: non_null
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  application:
    name: fileUpDwn
  redis:
    database: 0
  # servlet:
  #   multipart:
  #     max-file-size: 100MB
  #     max-request-size: 100MB
swagger:
  title: FileUpDwn
  description: 本地文件管理服务
  version: 1.0.0
# 文件配置
file:
  # 文件存储路径
  save-path: E:/temp/file-manager
  # 断点续传文件配置路径
  conf-path: E:/temp/file-manager/conf
ops:
  cmdb:
    filepath: /home/<USER>/libaray/ops-data-model/images
    #    tmpFilePath: /home/<USER>/libaray/ops-tt/
    basePath: /home/<USER>/libaray/ops-data-model/temp
    tmpFileUpload: /home/<USER>/libaray/ops-data-model/temp/tmp
    images: images
    mongodbHost: *********:27017:,*********:27017,*********:27017
    #是否读写分离
    readSecondary: false
    mongodbUserName: commondb
    mongodbPassWord: commondb^&*
    #mongodb 数据库名称
    mongodbDatabase: commondb
    mongodbDatabaseAcquisition: commondb
logging:
  config: classpath:logback-devops.xml
log:
  path: /home/<USER>/logs
  level:
    root: INFO




