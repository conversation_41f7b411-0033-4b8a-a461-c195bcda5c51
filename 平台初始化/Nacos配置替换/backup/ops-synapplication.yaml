ops:
  concurrentThreads: 30 #处理结果30个并发线程
  userDiamonThreadCron: 0/20 * * * * ? #30分钟检查一次监控线程是否还活着
  showDebug: true
  defaultDb: commondb
  adminDb: admin
  adminUserName: root
  adminPassword: root^&*
  subscriber:
    listenDb:
    #如果有更多的数据库需要监听在这后面增加
    - "mongodb://flow:flow^&*@*********:27017,*********:27017,*********:27017/flow"
    - "mongodb://script:script^&*@*********:27017,*********:27017,*********:27017/script"
    - "***************************************************************************************"
    - "mongodb://platform:platform^&*@*********:27017,*********:27017,*********:27017/platform"
    - "mongodb://platform-prod:platform-prod^&*@*********:27017,*********:27017,*********:27017/platform-prod"
    #订阅所在的库信息
    subConfigDb: commondb
    #是否开启订阅
    openSub: false
  cmdb:
    #mongodb
    mongodbUrl: "mongodb://commondb:commondb^&*@*********:27017,*********:27017,*********:27017/commondb"
    #mongodbUrl: ENC(u1GmK13r2j5pT+u4nqkxg3s4YCbFpHyBU+FMLilsIhgVzui5HG1UriAVLzovyQb09CnpvQQzQzP2qf35D2JqIgQQ41ZiIPLZ417iczkzRXreu4jFYSp2l8ZFc+hQNX67)
    #mongodb
    mongodbName: commondb
  tenantQuote:
    tables:
      - work_bench_category
      - work_bench_element_config
      - work_order_template_sort
      - work_order_template_info
      - flow_examples_shenheguanli
      - flow_shenheguanli
      - flow_category_relations
      - category
      - component_info
      - component_proxy
      - third_base
      - customView

spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  application:
    name: ops-synapplication
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1000MB
server:
  port: 7888
  tomcat:
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
    context-path: /
log:
  level: INFO
  path: /home/<USER>/application/ops-synapplication/logs
logging:
  config: classpath:logback-qz.xml
jasypt:
  encryptor:
    password: ajusdaujff
