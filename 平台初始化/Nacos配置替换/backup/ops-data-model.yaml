server:
  port: 7042
spring:
  main:
   allow-circular-references: true  #允许循环引用
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  application:
    name: ops-data-model
  redis:
    database: 0
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
ops:
  cmdb:
    filepath: /home/<USER>/libaray/ops-data-model/images
    #    tmpFilePath: /home/<USER>/libaray/ops-tt/
    basePath: /home/<USER>/libaray/ops-data-model/temp
    tmpFileUpload: /home/<USER>/libaray/ops-data-model/temp/tmp
    images: images
    mongodbHost: *********:27017:,*********:27017,*********:27017
    #是否读写分离
    readSecondary: false
    mongodbUserName: commondb
    mongodbPassWord: commondb
    #mongodb 数据库名称
    mongodbDatabase: commondb
    mongodbDatabaseAcquisition: commondb
logging:
  config: classpath:logback-devops.xml
log:
  path: /home/<USER>/logs
  level:
    root: INFO




