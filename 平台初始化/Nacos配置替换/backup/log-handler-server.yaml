server:
  port: 8821

spring:
  application:
    name: log-handler-server
  elasticsearch:
    rest:
      uris: [ "10.1.2.47:9200" ]
      username: cmdb
      password: devops
  kafka:
    listener:
      concurrency: 3
    consumer:
      group-id: log-handler
loghandler:
  server:
    listener-topics:
      default: default_log_topic
      servicea: aaa_topic
    log-max-length: 50000
    websocket-delay-retries: 5
    websocket: netty
    websocket-port: 11111
    websocket-application-name: log-handler-ws
  client:
    enabled: false

springdoc:
  swagger-ui:
    # url: /doc/openapi.json
    enabled: false
  api-docs:
    enabled: false


logging:
  config: classpath:logback-devops.xml
  level:
    root: INFO
    com.ops.log.server.service: DEBUG
    com.ops.log.server.web: DEBUG
    org.springframework.kafka.listener: WARN
    org.apache.kafka.clients.consumer: WARN
    com.alibaba.nacos.client.naming: WARN
    com.netflix.loadbalancer.BaseLoadBalancer: WARN
    org.apache.kafka.clients.consumer.internals: WARN
    org.springframework.data.repository.config: WARN
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: WARN
    com.netflix.config.ChainedDynamicProperty: WARN
    org.dozer.DozerBeanMapper: WARN
  file:
    path: /home/<USER>/logs

