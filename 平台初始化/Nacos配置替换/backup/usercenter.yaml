server:
  port: 7009

spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

server.tomcat.basedir: ./temp
log:
  path: /home/<USER>/logs
  level: 
    com.example.rtbootconsumer.service: debug
logging:
  config: classpath:logback-spring.xml


enableTokenCheck: false
enableLdap: true

tenant:
  emptyRole:
    filtration: true

syncDepartmentAndUser:
  corn: 0 0 0 1/1 * ?

max_size_key_word : 10

key_expireTime: 1800

platform_model: tenantModel

data.initialization.type: true
sso:
  serviceName: flow-sso
feign:
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
        logger-level: full
user:
  info:
    emptyRoleCode: default