swagger:
  enable: true
server:
  port: 10111
  tomcat:
    uri-encoding: UTF-8
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      force: true
      enabled: true
spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  application:
    name: ops-config-center
  servlet:
    multipart:
      max-file-size: 500MB
      file-size-threshold: 500MB
      max-request-size: 500MB
feign:
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
  # data:
  #   mongodb:
  #     uri: *********************************************************************************
springfox:
  documentation:
    enabled: false
    auto-startup: false

logging:
  config: classpath:logback-devops.xml
  level:
    root: INFO
    #    org.apache.http.wire: DEBUG
    #    org.apache.http.headers: DEBUG
    #    org.springframework.data.mongodb.core: error
    com.alibaba.nacos.client.naming: warn
    org.apache.kafka.clients.consumer.internals.AbstractCoordinator: warn
    org.springframework.data.repository.config: warn
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: warn
    com.netflix.config.ChainedDynamicProperty.checkAndFlip: warn
    org.dozer.DozerBeanMapper.loadFromFiles: warn

  file:
    path: /home/<USER>/logs
tempFileLocation: /home/<USER>/temp/
