server:
  port: 7055

spring:
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 512MB
      max-request-size: 512MB
  kafka:
    listener:
      concurrency: 3
    consumer:
      # 指定默认消费者group id --> 由于在kafka中，同一组中的consumer不会读取到同一个消息，依靠groud.id设置组名
      group-id: component-service
      max-poll-records: 5
  redis:
    database: 0
kafka:
  topic:
      third_consumer: ops_third_consumer_topic
#解决文件上传异常
server.tomcat.basedir: ./temp

logging:
  config: classpath:logback-devops.xml
  level:
    org.springframework.data.mongodb.core: error
    org.mongodb.driver.connection: error
    com.alibaba.nacos.client.naming: warn
    com.netflix.loadbalancer.BaseLoadBalancer: warn
    org.apache.kafka.clients.consumer.internals.AbstractCoordinator: warn
    org.springframework.data.repository.config: warn
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: warn
    com.netflix.config.ChainedDynamicProperty: warn
    org.dozer.DozerBeanMapper: warn
  path: /home/<USER>/logs

#默认开启令牌校验 false关闭
enableTokenCheck: false

scripts:
  filepath: /home/<USER>/library/scripts
  tempfilepath: /home/<USER>/tempfilepath/
  zipFilePath: /home/<USER>/zip/
  kafka:
    componentconsumertopic: componentComsumerTest
    end: component_end
    allocation: t_tenant_allocation_component
    third_consumer: ops_third_consumer_topic
    save_data: ops_result_consumer
    behaviorComponent:
      ing: behaviorComponentIng
      end: behaviorComponentEnd
  es:
    bus_log_index: compoent_bus_log

feign:
  httpclient:
    enabled: true

quickops:
  db:
    cron: 0/5 * * * * ?
    timeOut: 2 #数据库连接10分钟不操作自动断开连接
remoteExec:
  path: /tmp/component
  scriptPath: /tmp/component/script
  zipPath: /tmp/component
  utilScriptPath: /home/<USER>/application/component-service/utilScript
  # utilScriptPath: classpath:utilScript
  # path: D:/component
  # scriptPath: D:/component/script
  # zipPath: D:/component
  # utilScriptPath: D:/component/utilScript
  token: 3L5X8kH6n9zMm5onNFLVup3Gld8RhS5LQteGX4o8GNjRIOX25Xiyyh01u9n3sXtG1H5qV/DGVZnwzjbhr0tuDJqKTvuyvoyllfOjSzYAgDdiNYK4ILDmpMg8snq89yJX8G4IwOlE/+U49MDszyVu4AIB/FTSYoDrr5zxFQXCoiLAbAwMyT7nFoou5L0GHrp7
  callbackUrl: http://*********:7004/component-service/devops3/script/callback/direct
  downloadUrl: http://*********:7004/component-service/devops3/script/download/direct
  addTaskUrl: http://*********:23096/ansible/exec
exportTmpPath: /tmp/component/exportTmpPath

register:
  # tmpPath: C:/Users/<USER>/tmp
  # persistentPath: C:/Users/<USER>/data
  tmpPath: /tmp/component
  persistentPath: /data/component
  #/home/<USER>/application/ops-script-repertory/image-scirpt/dockerbuild_java.sh
  javaBuildScript: /home/<USER>/application/component-service/build/dockerbuild_java.sh
  uiBuildScript: /home/<USER>/application/component-service/build/dockerbuild_ui.sh
  containerJavaDeployScript: /home/<USER>/application/component-service/deploy/imagedeployback.py
  containerUiDeployScript: /home/<USER>/application/component-service/deploy/imagedeployfront.py
  deploySharePath: /home/<USER>/library
  platformDeployMethod: vm
  deployInfo:
    vmInitMem: 512
    vmInitMemUnit: MB
    vmMaxMem: 512
    vmMaxMemUnit: MB
    containerReplica: 1
    containerRequestMem: 128
    containerRequestMemUnit: Mi
    containerRequestCore: 0.5
    containerRequestCoreUnit: C
    containerLimitMem: 128
    containerLimitMemUnit: Mi
    containerLimitCore: 0.5
    containerLimitCoreUnit: C
  steps:
    - name: enterInfo
      show: 信息录入
      status: running
    - name: registerComponent
      show: 注册组件
      status: prepare
    # - name: buildImage
    #   show: 构建镜像
    #   status: prepare
    - name: deploy
      show: 部署
      status: prepare
info:
  recallUrl: http://*********:7100/component-service/callBack/direct
script:
  # createPath: G:\scriptdemo\data
  # releasePath: G:\scriptdemo\release
  createPath: /home/<USER>/script-file/data
  releasePath: /home/<USER>/script-file/release
  importRoot: /home/<USER>/script-file/import
  exportRoot: /home/<USER>/script-file/export
  url: http://**********:7068/apply/listUrlByQuery/direct

shell:
  aliasName: shellhelp
  suffixName: yml
python:
  aliasName: TypeConversion
  suffixName: py
permission:
  task: 0 0 0 * * ?
github:
  # 仓库地址
  repositoryPath: /home/<USER>/script-file/git_repository
  # repositoryPath: F:\git_repository
  # 是否开启单仓库
  enableSingelRepository: true
  #  脚本信息配置文件
  scriptConfigFileName: myconfig.yml
  #  忽略的文件 .git文件脚本管里不做处理
  ignoreFolders: .git
  enableSSHAuth: true
  password:
  username:
  privateKeyPath: /home/<USER>/script-file/privatekey/id_rsa
  # privateKeyPath: G:\\id_rsa

authUrl: http://*********:7100/usercenter/userAuth/oauth/token?username=t_admin&password=VUxr3a6pZqeM3uA80%2BjEsOdhfFUCflKUoG7THSDcaxuD8f2oorEa6Cg2ZXAnPXRpPGsHSZUGpza7ol57nWMnJd7nMBy9NcRNqPcZv5YfzzvNAfGZyAo4BEawQ2OXNSGJGQEtY%2F%2F8w4q7rPkQZiPD8XtvVm4B0CKPGwuHNp8%2B%2BqQ%3D
jasypt:
  encryptor:
    password: ajusdaujff
scriptComponentCode: script-engine
apiComponentCode: api-component
text_file_extensions: txt,doc,docx,rtf,htm,html,pdf,cpp,h,c,java,py,php,jsp,asp,xml,json,csv,md,log,yaml,yml,tex,sh,odt,cfg,ini,service,example,j2,bat,ps1