spring:
  application:
    name: low-code-web
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
server:
  port: 7081
enableTokenCheck: true
logging:
  config: classpath:logback-devops.xml
  level:
    root: INFO
    com.common.util: ERROR
    #org.apache.http.headers: DEBUG    


log:
  path: /home/<USER>/logs
  level:
    root: INFO
cache-seconds: 3600
low:
  dbType:
    - name: 开发数据库
      type: DEV
    - name: 測試数据库
      type: TEST

  platformUrl: http://10.1.2.39:7100/
  backUpPath: /home/<USER>/releaseBackUp/
  applyBusinessExecApi: http://apply-api-manage/SuperTenant/applyUseService/logic/orderExec/e2a70ab753a943f8ac41c0b5fd83c825
  noticeOwner: true
  validateOwner: false
  offset: 60
  nacos:
    url: http://10.1.2.47:8848
    username: nacos
    password: nacos
  applyConnectInfo:
    # url: http://10.1.2.39:7100/apply-api-manage/SuperTenant/releaseApply/logic/orderExec/1c7716e6d7ed424cb6081a0b20b62b33
    url: http://10.1.2.39:7100/low-code-web/v3/business/prod/exec/1c7716e6d7ed424cb6081a0b20b62b33
    authorization: bnkb5yedd6shdsht

db:
  prod: platform-prod
  dev: platform
subscriber:
  #订阅所在的库信息
  subConfigDb: commondb
offline-page:
  root: /home/<USER>/application/low-code-web
  dev: ${offline-page.root}/offline-page/dev
  prod: ${offline-page.root}/offline-page/prod
  version: ${offline-page.root}/offline-page/version
  backup: ${offline-page.root}/offline-page/backup