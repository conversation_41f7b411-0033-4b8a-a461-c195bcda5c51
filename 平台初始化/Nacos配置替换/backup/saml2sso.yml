server:
  port: 8778
spring:
  security:
    saml2:
      relyingparty:
        registration:
          autoengine:
            entity-id: https://auto-engine.starbucks.net/saml2sso/saml2/service-provider-metadata/{registrationId}
            acs:
              location: "https://auto-engine.starbucks.net/saml2sso/login/saml2/sso/{registrationId}"
              binding: POST
            identityprovider:
              entity-id: urn:VISC3CN:sts
              metadata-uri: https://sts-vis-cloud3.starbucks.com.cn/ofis/FederationMetadata/2007-06/FederationMetadata.xml
              verification:
                credentials:
                  - certificate-location: file:/media/agstar/_disk2/workspace/java/sso-saml2/src/main/resources/sts-vis-prod-cloud-s.starbucks.com.cn.cer.txt

              singlesignon:
                url: https://sts-vis-cloud3.starbucks.com.cn/ofis
                sign-request: false

plateform-url: https://auto-engine.starbucks.net
logoutServiceUrl: https://sts-vis-cloud3.starbucks.com.cn/ofis
Issuer: https://auto-engine.starbucks.net/saml2sso/saml2/service-provider-metadata/autoengine

logging:
  level:
    root: info
    org.springframework.security: trace