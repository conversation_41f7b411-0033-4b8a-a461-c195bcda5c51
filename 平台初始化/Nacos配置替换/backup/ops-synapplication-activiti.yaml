ops:
  subscriber:
    listenDb:
      #如果有更多的数据库需要监听在这后面增加
      #- ************************************************************************************************ #监听commondb
      - ************************************************************************************
  cmdb:
    scriptapplication: ops-script-repertory
    #mongodb
    mongodbUrl: *********************************************************************************************************
    #mongodbUrl: ENC(u1GmK13r2j5pT+u4nqkxg3s4YCbFpHyBU+FMLilsIhgVzui5HG1UriAVLzovyQb09CnpvQQzQzP2qf35D2JqIgQQ41ZiIPLZ417iczkzRXreu4jFYSp2l8ZFc+hQNX67)
    #mongodb
    mongodbName: public_param_db
    cmdbapplication: ops-cmdb
spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  application:
    name: ops-synapplication-activiti
  http:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
server:
  context-path: /
  port: 7048
  tomcat:
    uri-encoding: UTF-8

logging:
  config: classpath:logback-devops.xml

log:
  path: /home/<USER>/logs
  level:
    root: INFO
    
jasypt:
  encryptor:
    password: ajusdaujff
ribbon:
  eureka:
    enabled: true