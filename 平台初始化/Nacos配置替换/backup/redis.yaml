spring:
  redis:
    ssl: false
    database: 0
    password: "redis@cmdb3"
    lettuce:
      pool:
        max-active: 20
        max-idle: 20
        min-idle: 0
        time-between-eviction-runs: -1
    sentinel:
      password: "redis@cmdb3"
      nodes: *********:26379,*********:26379,*********:26379
      master: redis-master
  redisoptimizeconfiguration:
    commandTimeout: 600000
    redissonTimeout: 600000
      #redis:
      #host: ************
      #port: 6379
      #password: 123456
      #jedis:
      #pool:
      #max-active: 8
      #max-wait: -1
    #max-idle: 500
    #min-idle: 0
    # redis:
    #   host: ************
    #   port: 6379
    #   password: 123456
    