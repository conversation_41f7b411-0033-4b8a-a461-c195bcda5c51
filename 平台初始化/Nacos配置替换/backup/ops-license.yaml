server:
  port: 7222
license:
  info:
    #密钥库存储路径
    private-keys-store-path: /home/<USER>/application/ops-license/license/passPrivateKey.keystore
    #公钥存储路径
    public-keys-store-path: /home/<USER>/application/ops-license/license/passPublicCert.keystore
    #私钥密码
    private-key-pass: pass_8866
    #公钥密码
    public-key-pass: pass_8866
    #证书生成路径
    license-path: /home/<USER>/application/ops-license/license
    #证书subject
    subject: devops
    #公钥别称
    public-alias: passPublicCert
    #私钥别称
    private-alias: passPrivateKey
logging:
  config: classpath:logback-qz.xml
  level:
    com.alibaba.nacos.client.naming: warn
    com.netflix.loadbalancer.BaseLoadBalancer: warn
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: warn
    com.netflix.config.ChainedDynamicProperty: warn
  path: /home/<USER>/logs
