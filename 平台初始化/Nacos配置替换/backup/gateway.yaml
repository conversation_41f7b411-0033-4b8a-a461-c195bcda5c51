server:
  port: 7004

spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  redis:
    timeout: 3600
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
      enabled: true
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods:
              - GET
              - POST
              - DELETE
              - PUT
              - OPTION

server.tomcat.basedir: /home/<USER>/temp

  
logging:
  config: classpath:logback-devops.xml
  path: /home/<USER>/logs
  level:
    root: INFO
    com.gateway.service.impl: debug
    com.gateway.config: debug
    com.qz.common.util: WARN
    com.alibaba.nacos.client.naming: WARN
    com.netflix.loadbalancer.BaseLoadBalancer: WARN
    org.springframework.data.repository.config: WARN
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: WARN
    com.netflix.config.ChainedDynamicProperty: WARN


mas_head_name: MAS_REMOTE_USER
default_token_expiretime: 36000
intercept_api: openplatapi,orderExec
whiteUrls:
  - /userAuth/**
  - /sso/**
  - /flow-sso/**
  - /sso_login/**
  - /ssoAuthentication/**
  - /**/**/direct
  - /v3/convert/**
  - /interface/findInterfaceConfig
  - /file/showPictures/**
  - /licence/**
  - /login/saml2/sso/**
  - /saml2/**
  - /qz-auth/oauth/genToken
  - /qz-auth/**
  - /login/saml2/sso/**
  - /logined
  - /logout
  - /logout/**  
  - /json
  - /actuator/prometheus

authUrl:
  - /business/prod/exec/
  - /platform/logic/**
  