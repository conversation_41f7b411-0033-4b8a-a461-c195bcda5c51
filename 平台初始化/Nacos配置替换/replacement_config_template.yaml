# Nacos配置替换模板
# 请根据实际环境修改variables部分的值

global_replacements:
  description: "全局替换规则，适用于所有配置文件"
  rules:
    - name: "IP地址段替换"
      pattern: r"10\.1\.2\.(\d+)"
      replacement: "192.168.1.{host_suffix}"
      description: "将10.1.2.x替换为192.168.1.x，支持智能映射"
    
    - name: "平台地址替换"
      pattern: r"http://10\.1\.2\.39:7100/"
      replacement: "http://{platform_host}:7100/"
      description: "替换平台地址"
    
    - name: "网关地址替换"
      pattern: r"http://10\.1\.2\.39:7004/"
      replacement: "http://{gateway_host}:7004/"
      description: "替换网关地址"

file_specific_replacements:
  description: "针对特定文件的替换规则"
  
  # Redis配置替换
  "redis.yaml":
    - name: "Redis密码替换"
      pattern: r'password:\s*"redis@cmdb3"'
      replacement: 'password: "{redis_password}"'
      description: "替换Redis密码"
    
    - name: "Redis哨兵密码替换"
      pattern: r'password:\s*"redis@cmdb3"'
      replacement: 'password: "{redis_sentinel_password}"'
      description: "替换Redis哨兵密码"
    
    - name: "Redis哨兵节点替换"
      pattern: r"nodes:\s*10\.1\.2\.45:26379,10\.1\.2\.46:26379,10\.1\.2\.47:26379"
      replacement: "nodes: {redis_sentinel_nodes}"
      description: "替换Redis哨兵节点"

  # MongoDB配置替换
  "mongodb-script.yaml":
    - name: "MongoDB script库连接替换"
      pattern: r"**************************\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script"
      replacement: "mongodb://script:{mongodb_script_password}@{mongodb_hosts}/script"
      description: "替换MongoDB script库连接信息"

  # Kafka配置替换
  "kafka.yaml":
    - name: "Kafka服务器地址替换"
      pattern: r"bootstrap-servers:\s*10\.1\.2\.45:9092,10\.1\.2\.46:9092,10\.1\.2\.47:9092"
      replacement: "bootstrap-servers: {kafka_servers}"
      description: "替换Kafka服务器地址"

  # 通用配置替换
  "common.yaml":
    - name: "平台前缀替换"
      pattern: r"platform_prefix:\s*ae"
      replacement: "platform_prefix: {platform_prefix}"
      description: "替换平台前缀标识"
    
    - name: "ES前缀替换"
      pattern: r"prefix:\s*ae"
      replacement: "prefix: {es_prefix}"
      description: "替换Elasticsearch前缀"

  # ops-synapplication配置替换
  "ops-synapplication.yaml":
    - name: "管理员密码替换"
      pattern: r"adminPassword:\s*root\^\&\*"
      replacement: "adminPassword: {admin_password}"
      description: "替换管理员密码"
    
    - name: "MongoDB flow库连接替换"
      pattern: r"mongodb://flow:flow\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/flow"
      replacement: "mongodb://flow:{mongodb_flow_password}@{mongodb_hosts}/flow"
      description: "替换MongoDB flow库连接"
    
    - name: "MongoDB script库连接替换"
      pattern: r"mongodb://script:script\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/script"
      replacement: "mongodb://script:{mongodb_script_password}@{mongodb_hosts}/script"
      description: "替换MongoDB script库连接"
    
    - name: "MongoDB flow-prod库连接替换"
      pattern: r"********************************\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/flow-prod"
      replacement: "mongodb://flow-prod:{mongodb_flow_prod_password}@{mongodb_hosts}/flow-prod"
      description: "替换MongoDB flow-prod库连接"
    
    - name: "MongoDB platform库连接替换"
      pattern: r"mongodb://platform:platform\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/platform"
      replacement: "mongodb://platform:{mongodb_platform_password}@{mongodb_hosts}/platform"
      description: "替换MongoDB platform库连接"
    
    - name: "MongoDB platform-prod库连接替换"
      pattern: r"mongodb://platform-prod:platform-prod\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/platform-prod"
      replacement: "mongodb://platform-prod:{mongodb_platform_prod_password}@{mongodb_hosts}/platform-prod"
      description: "替换MongoDB platform-prod库连接"
    
    - name: "MongoDB commondb库连接替换"
      pattern: r"mongodb://commondb:commondb\^\&\*@10\.1\.2\.45:27017,10\.1\.2\.46:27017,10\.1\.2\.47:27017/commondb"
      replacement: "mongodb://commondb:{mongodb_commondb_password}@{mongodb_hosts}/commondb"
      description: "替换MongoDB commondb库连接"

variables:
  description: "替换变量定义 - 请根据实际环境修改这些值"
  
  # 主机地址配置
  platform_host: "*************"
  gateway_host: "*************"
  
  # IP地址映射配置
  host_suffix_mapping:
    "39": "100"    # 平台主机: ********* -> *************
    "45": "101"    # 数据库主机1: ********* -> *************
    "46": "102"    # 数据库主机2: ********* -> *************
    "47": "103"    # 数据库主机3: ********* -> *************
  
  # Redis配置
  redis_password: "redispass123"
  redis_sentinel_password: "redispass123"
  redis_sentinel_nodes: "*************:26379,*************:26379,*************:26379"
  
  # MongoDB配置
  mongodb_hosts: "*************:27017,*************:27017,*************:27017"
  mongodb_script_password: "scriptpass"
  mongodb_flow_password: "flowpass^&*"
  mongodb_flow_prod_password: "flowprodpass"
  mongodb_platform_password: "platformpass^&*"
  mongodb_platform_prod_password: "platformprodpass^&*"
  mongodb_commondb_password: "commondbpass^&*"
  
  # Kafka配置
  kafka_servers: "*************:9092,*************:9092,*************:9092"
  
  # 平台配置
  platform_prefix: "prod"
  es_prefix: "prod"
  
  # 管理员配置
  admin_password: "adminpass^&*"
