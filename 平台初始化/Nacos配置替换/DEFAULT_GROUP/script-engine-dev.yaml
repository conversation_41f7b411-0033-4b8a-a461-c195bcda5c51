script:
  script-path: /tmp/test-script
  execute-path: /tmp/exec-script
  expose-url: http://*********:7100/script-engine-dev
  identityFields: [ "area" ]
  clean-tmp-dir: true
sdk:
  db-name: platform
mongo-wrapper:
  db-name: platform

logging:
  level:
    root: INFO
    #    org.apache.http.wire: DEBUG
    #    org.apache.http.headers: DEBUG
    # com.ops.log.client.handler: DEBUG
    org.springframework.kafka.listener: WARN
    org.apache.kafka.clients.consumer: WARN
    com.common.util: ERROR
    org.springframework.data.mongodb.core: ERROR
    org.mongodb.driver.connection: ERROR
    com.alibaba.nacos.client.naming: WARN
    com.netflix.loadbalancer.BaseLoadBalancer: WARN
    org.apache.kafka.clients.consumer.internals.AbstractCoordinator: WARN
    org.springframework.data.repository.config: WARN
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: WARN
    com.netflix.config.ChainedDynamicProperty: WARN
    org.dozer.DozerBeanMapper: WARN