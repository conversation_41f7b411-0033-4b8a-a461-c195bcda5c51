server:
  port: 7078
  tomcat:
    basedir: data/temp
  
spring:
  application:
    name: apply-api-manage-prod
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  http:
    multipart:
      maxFileSize: 100Mb
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
    
log.level: INFO
logging:
  config: classpath:spring-logback.xml
  level:
    root: INFO
  file:
    path: /home/<USER>/logs/
jasypt:
  encryptor:
    password: ajusdaujff

swagger.enable: true


code:
  info:
    open_path: /home/<USER>/build/all/
    all_path: /home/<USER>/build/all/
    source_path: /home/<USER>/build/source/java-client

codegen:
  init_external_jar: true
  jar_folder: /home/<USER>/application/apply-api-manage-prod/lib
  jarFolderProd: /home/<USER>/application/apply-api-manage-prod/lib
  jar_url: /home/<USER>/application/apply-api-manage-prod/java-client-SuperTenant.jar
  magic_api_app_id: 844603e39a684865a8e367f704b306ee


magic-api:
  #配置web页面入口
  web: /magic/web
  resource:
    #配置存储在数据库中
    type: custom
    #配置文件存储位置。当以classpath开头时，为只读模式
    #location: /home/<USER>/data/magic-api
    # 使用数据库存储时的数据库名
    dbName: platform-prod
    # 数据库中的表名
    tableName: ae_magic_api
    #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    # datasource: magic
    # key前缀
    prefix: ""
    # 是否是只读模式
    readonly: false
  throwException: true

springfox:
  documentation:
    enabled: false
    auto-startup: false