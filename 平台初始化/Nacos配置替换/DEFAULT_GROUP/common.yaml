
#通用的密钥
jasypt:
  encryptor:
    password: ajus<PERSON>ujff

system:
  common:
    #平台标识
    platform_prefix: gq
    #平台地址
    platform_address: http://*************:7100/
    platform_gateway: http://*************:7004/
    #数据库示例名称
    db_info:
      commondb: commondb
      flow: flow
      script: script
      devops: devops
      public_param_db: public_param_db
    #扩展库名
    db_extend: 
      - key: devops_prod
        value: devops-prod
      - key: flow_prod
        value: flow-prod
      - key: platform
        value: platform
      - key: platform_prod
        value: platform-prod                
    #redis连接库名
 # redis:
    #database: 0
  es:
    prefix: gq
  kafka:
    prefix: gq


