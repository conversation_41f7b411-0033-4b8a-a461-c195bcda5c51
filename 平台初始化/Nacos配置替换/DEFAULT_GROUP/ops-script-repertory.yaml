server:
  port: 7045

spring:
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 512MB
      max-request-size: 512MB
  kafka:
    listener:
      concurrency: 3
    consumer:
      # 指定默认消费者group id --> 由于在kafka中，同一组中的consumer不会读取到同一个消息，依靠groud.id设置组名
      group-id: ops_script
      max-poll-records: 5
  redis:
    database: 0

#解决文件上传异常
server.tomcat.basedir: ../temp

logging:
  config: classpath:logback-devops.xml
  level:
    org.springframework.data.mongodb.core: error
    org.mongodb.driver.connection: error
    com.alibaba.nacos.client.naming: warn
    com.netflix.loadbalancer.BaseLoadBalancer: warn
    org.apache.kafka.clients.consumer.internals.AbstractCoordinator: warn
    org.springframework.data.repository.config: warn
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: warn
    com.netflix.config.ChainedDynamicProperty: warn
    org.dozer.DozerBeanMapper: warn
  path: /home/<USER>/logs

#默认开启令牌校验 false关闭
enableTokenCheck: false

scripts:
  filepath: /home/<USER>/library/scripts
  tempfilepath: /home/<USER>/tempfilepath/
  zipFilePath: /home/<USER>/zip/
  kafka:
    componentconsumertopic: componentComsumerTest
    end: component_end
    allocation: t_tenant_allocation_component
    third_consumer: ops_third_consumer_topic
    save_data: ops_result_consumer
    behaviorComponent:
      ing: behaviorComponentIng
      end: behaviorComponentEnd
  es:
    bus_log_index: compoent_bus_log

feign:
  httpclient:
    enabled: true

quickops:
  db:
    cron: 0/5 * * * * ?
    timeOut: 2 #数据库连接10分钟不操作自动断开连接
remoteExec:
  path: /tmp/component
  scriptPath: /tmp/component/script
  zipPath: /tmp/component
  utilScriptPath: /home/<USER>/application/ops-script-repertory/utilScript
  # utilScriptPath: classpath:utilScript
  # path: D:/component
  # scriptPath: D:/component/script
  # zipPath: D:/component
  # utilScriptPath: D:/component/utilScript
  token: 3L5X8kH6n9zMm5onNFLVup3Gld8RhS5LQteGX4o8GNjRIOX25Xiyyh01u9n3sXtG1H5qV/DGVZnwzjbhr0tuDJqKTvuyvoyllfOjSzYAgDdiNYK4ILDmpMg8snq89yJX8G4IwOlE/+U49MDszyVu4AIB/FTSYoDrr5zxFQXCoiLAbAwMyT7nFoou5L0GHrp7
  callbackUrl: http://*********:7004/ops-script-repertory/devops3/script/callback/direct
  downloadUrl: http://*********:7004/ops-script-repertory/devops3/script/download/direct
  addTaskUrl: http://*********:23096/ansible/exec
exportTmpPath: /tmp/component/exportTmpPath

register:
  # tmpPath: C:/Users/<USER>/tmp
  # persistentPath: C:/Users/<USER>/data
  tmpPath: /tmp/component
  persistentPath: /data/component
  #/home/<USER>/application/ops-script-repertory/image-scirpt/dockerbuild_java.sh
  javaBuildScript: /home/<USER>/application/ops-script-repertory/build/dockerbuild_java.sh
  uiBuildScript: /home/<USER>/application/ops-script-repertory/build/dockerbuild_ui.sh
  containerJavaDeployScript: /home/<USER>/application/ops-script-repertory/deploy/imagedeployback.py
  containerUiDeployScript: /home/<USER>/application/ops-script-repertory/deploy/imagedeployfront.py
  deploySharePath: /home/<USER>/library
  platformDeployMethod: vm
  deployInfo:
    vmInitMem: 512
    vmInitMemUnit: MB
    vmMaxMem: 512
    vmMaxMemUnit: MB
    containerReplica: 1
    containerRequestMem: 128
    containerRequestMemUnit: Mi
    containerRequestCore: 0.5
    containerRequestCoreUnit: C
    containerLimitMem: 128
    containerLimitMemUnit: Mi
    containerLimitCore: 0.5
    containerLimitCoreUnit: C
  steps:
    - name: enterInfo
      show: 信息录入
      status: running
    - name: registerComponent
      show: 注册组件
      status: prepare
    # - name: buildImage
    #   show: 构建镜像
    #   status: prepare
    - name: deploy
      show: 部署
      status: prepare