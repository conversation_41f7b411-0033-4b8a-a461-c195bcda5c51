server:
  port: 7047
spring:
  main:
    allow-circular-references: true  #允许循环引用
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
cmdb:
  platform:
    dev:
      url: http://cmdb.minzhi.cn
    prod:
      url: http://cmdb.minzhi.cn
connector:
  appId: YWNjZXNzQWRtaW4=
  appSecret: Y2I3MTFiMDZmZDRhNDJmYTk5ZjhhOGE5MjVjZWZmZTM=
  authorization_type: new
  path:
    - env: dev
      appId: YWNjZXNzQWRtaW4=
      appSecret: Y2I3MTFiMDZmZDRhNDJmYTk5ZjhhOGE5MjVjZWZmZTM=
      authorization_type: new
      authorization_path: http://cmdb.minzhi.cn/api/v2/auth/login
      select_path: http://cmdb.minzhi.cn/api/v2/data/view
      save_path: http://cmdb.minzhi.cn/api/v2/data/storage
    - env: prod
      appId: YWNjZXNzQWRtaW4=
      appSecret: Y2I3MTFiMDZmZDRhNDJmYTk5ZjhhOGE5MjVjZWZmZTM=
      authorization_type: new
      authorization_path: http://cmdb.minzhi.cn/api/v2/auth/login
      select_path: http://cmdb.minzhi.cn/api/v2/data/view
      save_path: http://cmdb.minzhi.cn/api/v2/data/storage
  module_name: data_source

logging:
  config: classpath:logback-devops.xml
  level:
    com.alibaba.nacos.client.naming: INFO
  path: /home/<USER>/application/logs

swagger.enable: true

