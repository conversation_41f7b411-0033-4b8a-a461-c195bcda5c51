server:
  port: 9001
  tomcat:
    #basedir: /home/<USER>/temp/flow-sso/
    basedir: /tmp/flow-sso/
spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  http:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB
  application:
    name: flow-sso

log.level: INFO
log.path: ./log
logging:
    config: classpath:logback-spring.xml

sso:
  userinfourl: https://graph.microsoft.com/oidc/userinfo
  # tokenurl: http://************:7200/auth/oauth/token
  tokenurl: xxx
  authorize:  https://login.microsoftonline.com/cef04b19-7776-4a94-b89b-375c77a8f936/oauth2/v2.0/authorize
  collbackurl: https://**************:7400/flow_sso/sso/callbackOfOidc
  indexurl: https://**************:7400/#/loginInIndex
  # logout_url: http://************:7200/auth/client_logout
  logout_url: https://login.microsoftonline.com/cef04b19-7776-4a94-b89b-375c77a8f936/oauth2/logout
  logout_redirect_uri: https://**************:7400/login#/login
  client_id: ff589879-2141-4d39-9fa7-42db3f5f99a0
  secret: 717ea7f5-6ad7-4c56-a299-d93363d78c7e
  tenantId: ENC(3HcXc2knmIYc8PYznLVH8qhisrQ97Hw7oOYVZAZIo157x2D6D35zYqevwy6S3Oy3)
  secretId: ENC(QH5npvqGf4UAOvoRJOfOQ4CpEXkNLTVvAxnTDRpjzxLY8ljCfmgBSBEGWrTJ9MOBDDUF4JCvvK8=)
  authTenant: common
  scope: email
  graphUserScopes: https://graph.microsoft.com/.default
  nonce: only_one
  action: form
  username: email
# sso:
#   userinfourl: https://test-demo2.ifeilian.com/api/oauth2/userinfo
#   tokenurl: https://test-demo2.ifeilian.com/api/oauth2/token
#   authorize: https://test-demo2.ifeilian.com/api/oauth2/authorize
#   collbackurl: http://192.168.2.227:7100/flow_sso/sso/callback
#   indexurl: http://192.168.2.227:7100/#/loginInIndex
#   client_id: ptKVJkVIFzVZurtyVzwwqHMzuloubwfhdNAQpxbF
#   secret: JSwZNXJBMFnjQbOJLZRJGcRMBdwdfKvyNNleSlwq
#   scope: users
#   action: form

ssoconfig:
  indexUrl: /#/login
  ssoLoginUrl: /login
  indexDirectUrl: /#/ssoLoginIn
  callBackUrl: /flow_sso/ssoAuthentication/callback
  logoutDirectUrl: /flow_sso/ssoAuthentication/sso_logout
  logoutTokenKey: tk
  state: linda
  scope: openid profile email
  nonce: only_one
  action: form
  username: username