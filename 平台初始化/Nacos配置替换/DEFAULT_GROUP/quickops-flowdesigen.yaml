flow:
  max-running-count: 1000
  max-waiting-count: 2000
  ws-monitor-port: 7048
  ws-monitor-name: flow-monitor-ws
  ws-status-port: 7041
  ws-status-name: flow-status-ws
  share-path: /home/<USER>/library
  report-template-path: classpath:flow_report_template.docx
  flow-mail-config-id: 953f1c7b8b594087b7b2ac85f337df3e
  node-mail-config-id: 953f1c7b8b594087b7b2ac85f337df3e
  coordination-mail-config-id: 452d6d10b69c4eebb9492f9e227fff89
  msg-topic: t_flowMsg_db1 # kafka topic
  temp-path: /home/<USER>/temp
  swagger-enabled: true
  domain-url: http://**************:7400
  heat-distribution-color:
    "EEEEEE": 
      - 0
      - 0
    "D9E591": 
      - 1
      - 5
    "98C470": 
      - 6
      - 10
    "5EA14D": 
      - 11
      - 15
    "35662C": 
      - 16
      - 1000
mongo-wrapper:
  db-name: flow

server:
  port: 7040
  tomcat:
    uri-encoding: UTF-8
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      force: true
      enabled: true
spring:
  application:
   name: quickops-flowdesigen
  servlet:
    multipart:
      max-file-size: 500MB
      file-size-threshold: 500MB
      max-request-size: 500MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************
    username: root
    password: 'shmz8866.'
  activiti:
    database-schema-update: true
    #    database-schema: activiti
    #    db-history-used: false
    history-level: none
    async-executor-enabled: true
  redis:
    database: 0
  kafka:
    listener:
      concurrency: 3
    consumer:
      # 指定默认消费者group id --> 由于在kafka中，同一组中的consumer不会读取到同一个消息，依靠groud.id设置组名
      group-id: flow-group
      max-poll-records: 5


springdoc:
  swagger-ui:
    # url: /doc/openapi.json
    enabled: true
  api-docs:
    enabled: true


logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    #org.apache.http.wire: DEBUG
    #org.apache.http.headers: DEBUG    
    com.ops.log.client.handler: INFO
    org.springframework.kafka.listener: WARN
    org.apache.kafka.clients.consumer: WARN
    com.common.util: WARN
    org.springframework.data.mongodb.core: ERROR
    org.mongodb.driver.connection: ERROR
    com.alibaba.nacos.client.naming: WARN
    com.netflix.loadbalancer.BaseLoadBalancer: WARN
    org.apache.kafka.clients.consumer.internals.AbstractCoordinator: WARN
    org.springframework.data.repository.config: WARN
    com.netflix.loadbalancer.DynamicServerListLoadBalancer: WARN
    com.netflix.config.ChainedDynamicProperty: WARN
    org.dozer.DozerBeanMapper: WARN
    com.ops.flowdesigen.webscoket:  INFO
  file:
    path: /home/<USER>/logs