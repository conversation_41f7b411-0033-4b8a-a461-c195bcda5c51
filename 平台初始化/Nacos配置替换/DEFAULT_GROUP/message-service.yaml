server:
  port: 7005
spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  application:
    name: message-service
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1000MB
  main:
    allow-bean-definition-overriding: true

  jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
  kafka:
    consumer:
      group-id: message-group
    topic:
      email: TOPIC_EMAIL
      sms: TOPIC_SMS
      wx: TOPIC_WX
      comm: TOPIC_COMM
  thymeleaf:
        mode: LEGACYHTML5
        cache: false


#解决文件上传异常
server.tomcat.basedir: data/temp

log:
  level: INFO
logging:
  config: classpath:logback-devops.xml
  path: /home/<USER>/logs

#默认开启令牌校验 false关闭
enableTokenCheck: false

enable_message_queue: false
#message:
#    emailAddresser: <EMAIL>
#    emailNickName: devops
#    emailPassword: Pass2017
#    emailSmtpHost: smtp.shqianzhi.net
#    emailSmtpPort: 465
#    emailSslEnable: true
#毫秒
 #  emailSmtpTimeout: 60000
#    attachDir: /temp/

