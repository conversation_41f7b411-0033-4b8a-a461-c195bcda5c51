event:
  param:
    list:
      - type: '0'
        pageId: 'xx'
        appId: ''
        flowId: ''
        flowInstanceId: 'flowInstanceId'
        procInstanceId: 'flowInstanceId'
        taskId: 'xx'
        nodeId: 'xx'
        workOrderId: 'xx'
        workOrderStatus: 'xx'
        organizationName: "flowInputParams.businessParam.onboardData.realityForm.zone"
        appSystem: "flowInputParams.businessParam.onboardData.realityForm.appSystemName"
      - type: '1'
        pageId: ''
        appId: 'flowInputParams.releaseDTO.release.appSystemId'
        flowId: ''
        flowInstanceId: 'flowInstanceId'
        procInstanceId: ''
        taskId: ''
        nodeId: ''
        workOrderId: 'xx'
        workOrderStatus: ''
        organizationName: ""
        appSystem: ""
      - type: '2'
        pageId: ''
        appId: ''
        flowId: 'flowId'
        flowInstanceId: 'flowInstanceId'
        procInstanceId: 'flowInstanceId'
        taskId: ''
        nodeId: ''
        workOrderId: ''
        workOrderStatus: ''
        organizationName: "flowInputParams.organizationName"
        appSystem: "flowInputParams.appSystem"
      - type: '3'
        pageId: ''
        appId: ''
        flowId: 'flowId'
        flowInstanceId: 'flowInstanceId'
        procInstanceId: 'flowInstanceId'
        taskId: ''
        nodeId: ''
        workOrderId: ''
        workOrderStatus: ''
        organizationName: "flowInputParams.organizationName"
        appSystem: "flowInputParams.appSystem"