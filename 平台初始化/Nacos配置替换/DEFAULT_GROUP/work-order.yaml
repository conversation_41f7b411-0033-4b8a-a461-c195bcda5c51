swagger:
  enable: true
  tomcat:
    uri-encoding: UTF-8
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      force: true
      enabled: true
server:
  port: 7053
spring:
  application:
    name: work-order
  servlet:
    multipart:
      max-file-size: 500MB
      file-size-threshold: 500MB
      max-request-size: 500MB
  kafka:
    listener:
      concurrency: 3
    consumer:
      group-id: flowdesigen-group
      max-poll-records: 5


logging:
  config: classpath:logback-devops.xml
  file:
    path: /home/<USER>/logs
  level:
    root: INFO    
    #    org.apache.http.wire: DEBUG
    #    org.apache.http.headers: DEBUG
    #    org.springframework.data.mongodb.core: error
data:
  initialization:
    type: false
    
  
order:
  api_level:
    - common
    - work_order
  urge-mail: 299115c3d18e49e2b33fad33bd02a0aa
  aa: aa
  template-assets-copy:
    # 忽略申请
    - 9465399e-edca-444a-9616-a7cb9d0c74a3
    # 延期申请
    - acf18621-74f5-4280-82f2-81c827ab5260
    # 可接受申请
    - a310ab6c-26be-415a-91b5-57ed8d909509
    # 发布审批流
    - 9e87a22b-f579-4375-b3e4-ad4d6b62b4de
  template-assets-quote:
    # 仓库初始化 gitlab
    - a18d36f6-b855-415c-b6a1-85da46ade3bc
    # 仓库初始化 svn
    - e0e73fc785f74f25992e3164de99b137
    # 应用初始化
    - c01eee53-4ebb-4630-93ec-4d7ab1d3a8cc
    # 租户权限申请
    - c5703817-7d15-4ea2-8ed4-d0642919f9e1
    # 应用权限申请
    - d2db0435-1f0c-4aee-a6a7-a3ab83076bc7
  sort-assets-quote:
    # 仓库初始化 
    - 1f5f6603ff394887bd0c6cdf9e463636
    # 应用初始化
    - 95804421e93140db9b5cfd1f61e60163
    # 安全漏洞模板
    - bbebdca654ee4c18a398541ca7147bce
    # 审批流
    - 6b646da837e94356965bfe32220f1161
    # # 内置
    - 5fbf5165a23d4bc5b7c0055a1e494d1a
    # 发布模板
    - eb8aaecb7e5544a8a67f57ad22de7805
    # 部署模板
    - f48c12baaf7c4536aedbd7463de981df
