upstream zuulserver {
    server 10.1.2.39:7004;
}

map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
    listen 7100;
    server_name 10.1.2.39;
    client_max_body_size 1000m;
    location / {
        root /home/<USER>/ui/master/dist;
        index index.html index.htm;
        if ($request_filename ~* (index.html|index.htm) ) {
            add_header Cache-Control no-sotre;
        }
    }
    location /imgs {
        root /home/<USER>/ui/;
        add_header Cache-Control no-sotre;
    }
    location ^~/zdyProject {
        root /home/<USER>/ui/zdyProject/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    location ^~/thirdPartyPage {
        root /home/<USER>/ui/thirdPartyPage/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    location ^~/configCenter {
        root /home/<USER>/ui/common/view/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }

    location ^~/commonWorkbench {
        root /home/<USER>/ui/workbench/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    location ^~/devops {
        root /home/<USER>/ui/devops/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    location ^~/components {
        root /home/<USER>/ui/ui-components/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    #location ^~/component_management {
    #   root /home/<USER>/ui/ui-components/dist;
    #   index index.html index.htm;
    #   add_header  Cache-Control  no-sotre;
    #}
    location ^~/maintenanceproject {
        root /home/<USER>/ui/quickopsui/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    location ^~/workOrder {
        root /home/<USER>/ui/workOrder/dist;
        index index.html index.htm;
        add_header Cache-Control no-sotre;
    }
    location ^~/ops-script-repertory {
        root /home/<USER>/ui/ops-component/dist;
        index index.html index.htm;
        proxy_pass http://zuulserver/component-service;
        add_header Cache-Control no-sotre;
    }

    location ^~/flow_sso {
        proxy_pass http://zuulserver/flow-sso/;
    }
    location ^~/login {
        proxy_pass http://zuulserver/flow-sso/ssoAuthentication/sso_auth;
    }

    location ^~/sso {
        proxy_pass http://zuulserver/flow-sso/ssoAuthentication/sso_auth;
    }

    location ^~/recall {
        rewrite "^/recall/(.*)" /$1 break;
        proxy_pass http://*********:8080;
        proxy_set_header Host $host;
    }
    location /webRecall {
        proxy_pass http://*********:8080/webRecall;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }
    location ^~/elasticsearch {
        proxy_pass http://zuulserver/elasticsearch;
    }

    #location ^~/sso {
    #    proxy_pass http://zuulserver/salm2sso;
    #}

    location ^~/salm2sso {
        proxy_pass http://zuulserver/salm2sso;
    }
    location ^~/usercenter {
        proxy_pass http://zuulserver/usercenter;
    }
    location ^~/gateway {
        proxy_pass http://zuulserver/usercenter;
    }
    location ^~/project-manager {
        proxy_pass http://zuulserver/project-manager;
    }
    location ^~/security-vulnerability {
        proxy_pass http://zuulserver/security-vulnerability;
    }
    location ^~/source-code {
        proxy_pass http://zuulserver/source-code;
    }
    location ^~/repo-source-code {
        proxy_pass http://zuulserver/repo-source-code;
    }
    location ^~/script {
        proxy_pass http://zuulserver/zuul/component-service;
    }

    location ^~/innercmdb {
        proxy_pass http://zuulserver/ops-data-model;
    }
    location ^~/platform-docking {
        proxy_pass http://zuulserver/platform-docking;
    }
    location ^~/message {
        proxy_pass http://zuulserver/message-service;
    }
    location ^~/azuredevops-server-sq {
        proxy_pass http://zuulserver/azuredevops-server-sq;
    }

    location ^~/tools-pipeline {
        proxy_pass http://zuulserver/tools-pipeline;
    }

    location ^~/synapplication {
        proxy_pass http://zuulserver/ops-synapplication;
    }

    location ^~/ops-flowdesigen {
        proxy_pass http://zuulserver/quickops-flowdesigen;
    }
    location ^~/quickops-flowdesigen {
        proxy_pass http://zuulserver/quickops-flowdesigen;
    }
    location ^~/sonar-server {
        proxy_pass http://zuulserver/sonar-server;
    }

    location ^~/low-code-web {
        proxy_pass http://zuulserver/low-code-web;
    }

    location ^~/scheduled-proxy {
        proxy_pass http://zuulserver/scheduled-proxy;
    }
    location ^~/common-workbench {
        proxy_pass http://zuulserver/common-workbench;
    }
    location ^~/config-center {
        proxy_pass http://zuulserver/ops-config-center;
    }
    location ^~/work-order {
        proxy_pass http://zuulserver/work-order;
    }
    location ^~/api/v1 {
        rewrite "^/api/v1/(.*)" /ops-flowdesigen/api/v1/$1/direct last;
    }

    location ^~/flowNodeCoordinationFile/viewOnline {
        rewrite "^/flowNodeCoordinationFile/viewOnline/(.*)" /ops-flowdesigen/flowNodeCoordinationFile/viewOnline/$1/direct last;
    }

    location ^~/ops-onboard {
        proxy_pass http://zuulserver/ops-onboard;
    }
    location ^~/flow-proxy {
        proxy_pass http://zuulserver/flow-proxy;
    }

    location ^~/release-management {
        proxy_pass http://zuulserver/release-management;
    }
    location ^~/media {
        proxy_pass http://zuulserver/media-package;
    }
    location ^~/media-package {
        proxy_pass http://zuulserver/media-package;
    }
    location ^~/media-upload-server {
        proxy_pass http://zuulserver/media-upload-server;
    }
    location ^~/media-download-server {
        proxy_pass http://zuulserver/media-download-server;
    }

    location ^~/workbench-report {
        proxy_pass http://zuulserver/workbench-report;
    }
    location ^~/third-exec {
        proxy_pass http://zuulserver/component-service;
    }
    location ^~/warehouse-statistics {
        proxy_pass http://zuulserver/warehouse-statistics;
    }
    location ^~/jenkins-server {
        proxy_pass http://zuulserver/jenkins-server;
    }

    location ^~/log-handler-server {
        proxy_pass http://zuulserver/log-handler-server;
    }

    location ^~/deploy-server {
        proxy_pass http://zuulserver/deploy-server;
    }

    location ^~/cmdb {
        proxy_pass http://zuulserver/ops-data-model;
    }
    location ^~/log-handler-ws {
        proxy_pass http://zuulserver/log-handler-ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }

    location ^~/flow-monitor-ws {
        proxy_pass http://zuulserver/flow-monitor-ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }
    location ^~/ops-script-repertory-ws {
        proxy_pass http://zuulserver/ops-script-repertory-ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }

    location ^~/flow-status-ws {
        proxy_pass http://zuulserver/flow-status-ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }

    location ^~/deploy-server-ws {
        proxy_pass http://zuulserver/deploy-server-ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }
    location ^~/build-component {
        proxy_pass http://zuulserver/build-component;

    }
    location ^~/platform-component {
        proxy_pass http://zuulserver/platform-component;
    }
    location ^~/file-store {
        proxy_pass http://zuulserver/file-store;
    }
    location ^~/security-component {
        proxy_pass http://zuulserver/security-component;
    }
    location ^~/release-server {
        proxy_pass http://zuulserver/devops-release;
    }
    location ^~/apply-api-manage/ {
        proxy_pass http://zuulserver/apply-api-manage/;
    }
    location ^~/apply-api-manage-prod/ {
        proxy_pass http://zuulserver/apply-api-manage-prod/;
    }
    location ^~/gw_api/ {
        proxy_pass http://zuulserver/;
    }
    location ^~/script-manager {
        proxy_pass http://zuulserver/component-service;
    }
    location ^~/script-engine {
        proxy_pass http://zuulserver/script-engine;
    }
    location ^~/codegen-server/ {
        proxy_pass http://zuulserver/apply-api-manage/;
    }
    location ^~/component-service/ {
        proxy_pass http://zuulserver/component-service;
    }
    location ^~/platform-docking-service {
        proxy_pass http://zuulserver/platform-docking-service;
    }
    location ^~/magicui {
        rewrite ^/magicui(.*)$ /magic/web$1 break;
        proxy_pass http://*********:7068;
    }
    location ^~/low-code-web-ws {
        proxy_pass http://zuulserver/low-code-web-ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_read_timeout 600s;
    }


}

